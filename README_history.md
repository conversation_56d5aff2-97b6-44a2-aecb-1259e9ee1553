# 历史数据生成器使用说明

## 概述
`history_data.go` 是一个专门用于生成MQTT历史数据的工具，支持补充前1、2、3个月的数据。

## ⚠️ 重要说明
**历史数据生成器完全独立运行，不会影响实时数据：**
- ✅ 使用独立的 `historyKWhMap` 存储历史kWh数据
- ✅ 与实时运行的 `deviceKWhMap` 完全分离
- ✅ 不会读取或修改 `kwh.txt` 文件
- ✅ 历史数据的kWh累积是独立计算的
- ✅ 可以与实时模拟器同时运行而不冲突

## 功能特性

### 📊 数据类型
- **中转罐数据**: 搅拌速度、电流、压力、重量等
- **搅拌机常规数据**: 分散速度、搅拌速度、电流、压力、温度等
- **搅拌机电能耗数据**: kWh、电压、电流、功率等

### ⏰ 时间配置
- **数据密度**: 每天1440条记录（每分钟一条）
- **时间范围**: 支持生成1、2、3个月前的数据
- **时间戳**: 精确到毫秒的历史时间戳

### 🚀 性能特性
- **并发处理**: 10个协程同步执行
- **负载均衡**: 设备均匀分配到各协程
- **速度控制**: 内置发送间隔控制，避免过载

## 使用方法

### 1. 编译程序
```bash
go build -o history_generator history_data.go
```

### 2. 运行命令

**生成1个月前的数据:**
```bash
./history_generator -months=1
```

**生成2个月前的数据:**
```bash
./history_generator -months=2
```

**生成3个月前的数据:**
```bash
./history_generator -months=3
```

### 3. 参数说明
- `-months`: 指定生成几个月前的数据 (必须是1、2或3)

## 数据独立性保证

### 🔒 完全隔离的数据系统
```
实时数据系统 (main.go):
├── deviceKWhMap (实时kWh累积)
├── kwh.txt (持久化文件)
└── 实时MQTT消息

历史数据系统 (history_data.go):
├── historyKWhMap (历史kWh累积)
├── 独立的初始化逻辑
└── 历史MQTT消息
```

### 🛡️ 安全机制
- **内存隔离**: 使用不同的变量名和作用域
- **文件隔离**: 不读取或写入kwh.txt文件
- **时间隔离**: 使用历史时间戳，不影响当前时间
- **进程隔离**: 可独立运行，不依赖实时程序

## 数据生成逻辑

### 📈 历史kWh累积算法
```
历史初始值 = 基础值 + 月份偏移 (1个月前=1500, 2个月前=2000, 3个月前=2500)
每分钟增量 = 当前功率(kW) × (1分钟/60分钟) + 随机波动(±10%)
历史累积值 = 前一分钟历史值 + 当前增量
```

### 🎯 数据真实性
- 使用锚定随机数，确保数据围绕合理值波动
- 电能耗数据基于实际功率计算累积
- 时间戳精确对应历史时间点

### 📊 数据量估算
```
单设备单月数据量 = 30天 × 1440条/天 = 43,200条
中转罐总数据量 = 53设备 × 43,200条 = 2,289,600条
搅拌机数据量 = 35设备 × 43,200条 × 2类型 = 3,024,000条
月总数据量 ≈ 530万条
```

## 运行示例

### 启动输出
```
开始生成 1 个月前的历史数据...
数据时间范围: 2024-01-01 到 2024-02-01
成功从 transfer.txt 加载 53 个设备的kWh数据
成功从 stirmech.txt 加载 35 个设备的kWh数据
设备数量: 中转罐 53 个, 搅拌机 35 个
协程 0 负责: 6个中转罐, 4个搅拌机
协程 1 负责: 5个中转罐, 4个搅拌机
...
历史数据生成开始，使用10个协程...
```

### 运行过程
```
协程 0 正在生成 2024-01-01 的数据...
协程 1 正在生成 2024-01-01 的数据...
[History] sub/devices/telemetry/68cd003f1fcabbf5017ce976 -> {"ts":1704067200000,"device_id":"68cd003f1fcabbf5017ce976",...}
[History] sub/devices/telemetry/68cd003f1fcabbf5017ce953-EC -> {"ts":1704067260000,"device_id":"68cd003f1fcabbf5017ce953",...}
...
```

### 完成输出
```
协程 0 完成历史数据生成
协程 1 完成历史数据生成
...
历史数据生成完成！共生成了 1 个月前的数据
```

## 注意事项

### ⚠️ 重要提醒
1. **数据量巨大**: 单月数据约530万条，请确保MQTT服务器和网络能够承受
2. **运行时间**: 根据网络和服务器性能，可能需要数小时完成
3. **资源占用**: 程序运行期间会占用一定的CPU和内存资源

### 🛡️ 安全措施
- 支持Ctrl+C优雅退出
- 内置发送速度控制(10ms间隔)
- 自动重连机制

### 📁 依赖文件
确保以下文件存在于同一目录:
- `transfer.txt` - 中转罐设备列表
- `stirmech.txt` - 搅拌机设备列表

## 故障排除

### 常见问题
1. **连接失败**: 检查MQTT服务器地址和认证信息
2. **文件不存在**: 确保设备列表文件存在且格式正确
3. **内存不足**: 可以考虑分批次运行或减少协程数量

### 日志分析
程序会输出详细的运行日志，包括:
- 设备加载状态
- 协程分配情况
- 数据生成进度
- 错误信息

## 技术细节

### 🏗️ 架构设计
- **独立运行**: 使用build ignore标签，避免与main.go冲突
- **模块化**: 功能函数独立，便于维护
- **并发安全**: 使用互斥锁保护共享数据

### 📡 MQTT配置
```go
broker := "tcp://dev-emqx-listener.redcoast.info:1883"
username := "admin"
password := "redcoast@123"
```

### 🎛️ 可调参数
- 协程数量: 默认10个 (可在代码中修改)
- 发送间隔: 默认10ms (可在代码中调整)
- 数据范围: 各传感器的数值范围可自定义
