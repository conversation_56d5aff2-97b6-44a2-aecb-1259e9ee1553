module mqtt-simulate

go 1.23.4

require github.com/eclipse/paho.mqtt.golang v1.5.0

require (
	github.com/apache/arrow/go/v15 v15.0.2 // indirect
	github.com/apapsch/go-jsonmerge/v2 v2.0.0 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/google/flatbuffers v24.3.7+incompatible // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/influxdata/line-protocol v0.0.0-20200327222509-2487e7298839 // indirect
	github.com/influxdata/line-protocol/v2 v2.2.1 // indirect
	github.com/klauspost/compress v1.17.7 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/oapi-codegen/runtime v1.0.0 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/zeebo/xxh3 v1.0.2 // indirect
	golang.org/x/exp v0.0.0-20240222234643-814bf88cf225 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
	golang.org/x/text v0.18.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	golang.org/x/xerrors v0.0.0-20231012003039-104605ab7028 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240903143218-8af14fe29dc1 // indirect
	google.golang.org/grpc v1.68.0 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
)

require (
	github.com/InfluxCommunity/influxdb3-go v0.14.0
	github.com/gorilla/websocket v1.5.3 // indirect
	github.com/influxdata/influxdb-client-go/v2 v2.14.0
	golang.org/x/net v0.29.0 // indirect
	golang.org/x/sync v0.8.0 // indirect
)
