//go:build ignore
// +build ignore

// 历史数据生成器
//
// 重要说明：
// 1. 这是一个独立的历史数据生成工具，不会影响实时运行的main.go程序
// 2. 历史数据的kWh累积是独立计算的，不会影响实时数据的kWh值
// 3. 使用独立的historyKWhMap存储历史kWh数据，与实时的deviceKWhMap完全分离
// 4. 生成的历史数据仅用于补充历史记录，不会干扰当前运行状态

package main

import (
	"bufio"
	"encoding/json"
	"flag"
	"fmt"
	"math/rand"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// 辅助函数定义（为了独立运行）

// SimulateFloat 生成 [left, right] 范围内的随机 float64 数，保留2位小数
func SimulateFloat(left, right float64) float64 {
	value := left + rand.Float64()*(right-left)
	return float64(int(value*100+0.5)) / 100
}

// SimulateFloatWithAnchor 生成带锚定值的随机 float64 数，保留2位小数
func SimulateFloatWithAnchor(left, right, anchor, anchorProbability float64) float64 {
	if anchor == 0 {
		return SimulateFloat(left, right)
	}

	if anchor < left || anchor > right {
		return SimulateFloat(left, right)
	}

	if anchorProbability < 0 {
		anchorProbability = 0
	}
	if anchorProbability > 1 {
		anchorProbability = 1
	}

	if rand.Float64() < anchorProbability {
		rangeSize := right - left
		deviation := rangeSize * 0.1

		minVal := anchor - deviation
		maxVal := anchor + deviation

		if minVal < left {
			minVal = left
		}
		if maxVal > right {
			maxVal = right
		}

		value := minVal + rand.Float64()*(maxVal-minVal)
		return float64(int(value*100+0.5)) / 100
	} else {
		return SimulateFloat(left, right)
	}
}

// 从map中提取所有的keys
func getKeysFromMap(deviceMap map[string]string) []string {
	keys := make([]string, 0, len(deviceMap))
	for key := range deviceMap {
		keys = append(keys, key)
	}
	return keys
}

// 读取txt文件并解析为map[id]name
func loadDeviceMap(filename string) (map[string]string, error) {
	deviceMap := make(map[string]string)

	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("无法打开文件 %s: %v", filename, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNum := 0
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		parts := strings.Split(line, ",")
		if len(parts) != 2 {
			return nil, fmt.Errorf("文件 %s 第 %d 行格式错误: %s", filename, lineNum, line)
		}

		id := strings.TrimSpace(parts[0])
		name := strings.TrimSpace(parts[1])
		deviceMap[id] = name
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取文件 %s 时出错: %v", filename, err)
	}

	return deviceMap, nil
}

// 历史数据生成器配置
type HistoryConfig struct {
	MonthsAgo     int // 几个月前 (1, 2, 3)
	StartDate     time.Time
	EndDate       time.Time
	Interval      time.Duration // 1分钟间隔
	RecordsPerDay int           // 每天1440条记录
}

// 历史数据kWh管理（独立于实时数据）
// 注意：这个map只用于历史数据生成，不会影响实时运行的deviceKWhMap
var historyKWhMap = make(map[string]float64)
var historyKWhMutex sync.Mutex

// 初始化历史kWh数据（独立初始化，不影响实时数据）
func initHistoryKWh(deviceIDs []string, monthsAgo int) {
	historyKWhMutex.Lock()
	defer historyKWhMutex.Unlock()

	fmt.Printf("初始化历史数据的kWh值（%d个月前）...\n", monthsAgo)

	for _, deviceID := range deviceIDs {
		// 根据月份设置不同的历史初始值
		// 越早的月份初始值越小，模拟历史累积过程
		baseValue := float64(1000 + monthsAgo*500) // 1个月前=1500, 2个月前=2000, 3个月前=2500
		initialValue := SimulateFloatWithAnchor(baseValue, baseValue+1000, baseValue+500, 0.5)
		historyKWhMap[deviceID] = initialValue
	}

	fmt.Printf("历史kWh数据初始化完成，共 %d 个设备\n", len(deviceIDs))
}

// 生成历史时间戳
func generateHistoryTimestamp(baseTime time.Time, minuteOffset int) int64 {
	return baseTime.Add(time.Duration(minuteOffset) * time.Minute).UnixMilli()
}

// 构建历史中转罐数据
func buildHistoryTransferTankPayload(deviceID string, timestamp int64) map[string]interface{} {
	return map[string]interface{}{
		"ts":        timestamp,
		"device_id": deviceID,
		"type":      "transfertank",
		"data": map[string]interface{}{
			"stir_spd":      SimulateFloatWithAnchor(19.8, 20.2, 20.0, 0.8),
			"stir_curr":     SimulateFloatWithAnchor(4.9, 10.0, 5.0, 0.7),
			"press":         SimulateFloatWithAnchor(-1, 5, 5, 0.9),
			"input_weight":  SimulateFloatWithAnchor(20, 60, 40, 0.6),
			"output_weight": SimulateFloatWithAnchor(20, 60, 40, 0.6),
			"rt_weight":     SimulateFloatWithAnchor(20, 60, 40, 0.6),
		},
	}
}

// 构建历史搅拌机常规数据
func buildHistoryStirmechPayload(deviceID string, timestamp int64) map[string]interface{} {
	return map[string]interface{}{
		"ts":        timestamp,
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"disperse_spd":  SimulateFloatWithAnchor(595, 605, 600, 0.8),
			"stir_spd":      SimulateFloatWithAnchor(18, 22, 20, 0.8),
			"disperse_curr": SimulateFloatWithAnchor(4.9, 5.2, 5, 0.9),
			"stir_curr":     SimulateFloatWithAnchor(4.9, 5.2, 5, 0.9),
			"press":         SimulateFloatWithAnchor(-1, 5, 5, 0.9),
			"temp":          SimulateFloatWithAnchor(23, 30, 26.5, 0.8),
			"worktime":      timestamp / 1000, // 转换为秒
		},
	}
}

// 构建历史搅拌机电能耗数据
func buildHistoryStirmechECPayload(deviceID string, timestamp int64) map[string]interface{} {
	i := SimulateFloatWithAnchor(4.9, 10.0, 5.0, 0.7)
	v := SimulateFloatWithAnchor(400, 450, 425, 0.7)
	kW := (i * v) / 1000

	// 历史数据的kWh累积计算（独立于实时数据）
	historyKWhMutex.Lock()
	currentKWh := historyKWhMap[deviceID]
	// 每分钟的增量
	increment := kW * (1.0 / 60.0) // 1分钟转换为小时
	fluctuation := increment * (SimulateFloat(-10, 10) / 100.0)
	currentKWh += increment + fluctuation
	historyKWhMap[deviceID] = currentKWh
	historyKWhMutex.Unlock()

	// 注意：这里的kWh是历史数据的独立累积值，不会影响实时运行的kWh数据
	return map[string]interface{}{
		"ts":        timestamp,
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"kWh": float64(int(currentKWh*100+0.5)) / 100,
			"v":   v,
			"i":   i,
			"kW":  float64(int(kW*100+0.5)) / 100,
		},
	}
}

// 发送历史数据消息
func sendHistoryMessage(client mqtt.Client, deviceID string, devType string, payload map[string]interface{}, suffix string) {
	finalDeviceID := deviceID + suffix
	topic := fmt.Sprintf("sub/%s/telemetry/%s", devType, finalDeviceID)
	jsonBytes, _ := json.Marshal(payload)
	token := client.Publish(topic, 0, false, jsonBytes)
	token.Wait()
	fmt.Printf("[History] %s -> %s\n", topic, string(jsonBytes))
}

// 历史数据生成协程
func generateHistoryData(client mqtt.Client, config HistoryConfig, transferTankIDs, stirMechIDs []string, goroutineID int, wg *sync.WaitGroup) {
	defer wg.Done()

	// 计算该协程负责的设备
	var assignedTransferTanks, assignedStirMechs []string
	for idx := goroutineID; idx < len(transferTankIDs); idx += 10 {
		assignedTransferTanks = append(assignedTransferTanks, transferTankIDs[idx])
	}
	for idx := goroutineID; idx < len(stirMechIDs); idx += 10 {
		assignedStirMechs = append(assignedStirMechs, stirMechIDs[idx])
	}

	fmt.Printf("协程 %d 负责: %d个中转罐, %d个搅拌机\n",
		goroutineID, len(assignedTransferTanks), len(assignedStirMechs))

	// 按天生成数据
	currentDate := config.StartDate
	for currentDate.Before(config.EndDate) {
		fmt.Printf("协程 %d 正在生成 %s 的数据...\n", goroutineID, currentDate.Format("2006-01-02"))

		// 每天1440条记录（每分钟一条）
		for minute := 0; minute < config.RecordsPerDay; minute++ {
			timestamp := generateHistoryTimestamp(currentDate, minute)

			// 生成中转罐数据
			for _, deviceID := range assignedTransferTanks {
				payload := buildHistoryTransferTankPayload(deviceID, timestamp)
				sendHistoryMessage(client, deviceID, "devices", payload, "")
			}

			// 生成搅拌机常规数据
			for _, deviceID := range assignedStirMechs {
				payload := buildHistoryStirmechPayload(deviceID, timestamp)
				sendHistoryMessage(client, deviceID, "devices", payload, "")
			}

			// 生成搅拌机电能耗数据
			for _, deviceID := range assignedStirMechs {
				payload := buildHistoryStirmechECPayload(deviceID, timestamp)
				sendHistoryMessage(client, deviceID, "devices", payload, "-EC")
			}

			// 控制发送速度，避免过快
			time.Sleep(10 * time.Millisecond)
		}

		// 移动到下一天
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	fmt.Printf("协程 %d 完成历史数据生成\n", goroutineID)
}

func main() {
	// 命令行参数
	monthsAgo := flag.Int("months", 1, "生成几个月前的数据 (1, 2, 3)")
	flag.Parse()

	if *monthsAgo < 1 || *monthsAgo > 3 {
		fmt.Println("错误: months参数必须是1, 2, 或3")
		return
	}

	fmt.Printf("开始生成 %d 个月前的历史数据...\n", *monthsAgo)

	// MQTT配置
	broker := "tcp://dev-emqx-listener.redcoast.info:1883"
	username := "admin"
	password := "redcoast@123"

	// 计算时间范围
	now := time.Now()
	endDate := now.AddDate(0, -*monthsAgo, 0)
	startDate := endDate.AddDate(0, -1, 0) // 一个月的数据

	config := HistoryConfig{
		MonthsAgo:     *monthsAgo,
		StartDate:     startDate,
		EndDate:       endDate,
		Interval:      time.Minute,
		RecordsPerDay: 1440,
	}

	fmt.Printf("数据时间范围: %s 到 %s\n",
		startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	// 加载设备数据
	TransTank, err := loadDeviceMap("transfer.txt")
	if err != nil {
		fmt.Printf("加载transfer.txt失败: %v\n", err)
		return
	}

	StirMech, err := loadDeviceMap("stirmech.txt")
	if err != nil {
		fmt.Printf("加载stirmech.txt失败: %v\n", err)
		return
	}

	transferTankIDs := getKeysFromMap(TransTank)
	stirMechIDs := getKeysFromMap(StirMech)

	// 初始化历史kWh数据
	initHistoryKWh(stirMechIDs, *monthsAgo)

	fmt.Printf("设备数量: 中转罐 %d 个, 搅拌机 %d 个\n", len(transferTankIDs), len(stirMechIDs))

	// 创建MQTT客户端
	opts := mqtt.NewClientOptions().
		AddBroker(broker).
		SetClientID("history-data-generator").
		SetUsername(username).
		SetPassword(password)

	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}
	defer client.Disconnect(250)

	// 信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		fmt.Println("\n收到退出信号，正在停止...")
		client.Disconnect(250)
		os.Exit(0)
	}()

	// 启动10个协程生成历史数据
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go generateHistoryData(client, config, transferTankIDs, stirMechIDs, i, &wg)
	}

	fmt.Println("历史数据生成开始，使用10个协程...")
	wg.Wait()

	fmt.Printf("历史数据生成完成！共生成了 %d 个月前的数据\n", *monthsAgo)
}
