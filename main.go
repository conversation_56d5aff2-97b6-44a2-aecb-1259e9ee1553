package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"math/rand"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// SimulateFloat 生成 [left, right] 范围内的随机 float64 数
func SimulateFloat(left, right int) float64 {
	return float64(left) + rand.Float64()*float64(right-left)
}

// 构建 payload 数据
func buildTransferTankPayload(deviceID string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "transfertank",
		"data": map[string]interface{}{
			"stir_spd":      SimulateFloat(100, 500),
			"stir_curr":     SimulateFloat(20, 60),
			"press":         SimulateFloat(20, 60),
			"input_weight":  SimulateFloat(20, 60),
			"output_weight": SimulateFloat(20, 60),
			"rt_weight":     Simulate<PERSON>loat(20, 60),
		},
	}
}

func buildStirmechPayload(deviceID string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"disperse_spd":  SimulateFloat(100, 500),
			"stir_spd":      SimulateFloat(20, 60),
			"disperse_curr": SimulateFloat(20, 60),
			"stir_curr":     SimulateFloat(20, 60),
			"press":         SimulateFloat(20, 60),
			"temp":          SimulateFloat(20, 60),
			"worktime":      time.Now().Unix(),
		},
	}
}

func buildStirmechECPayload(deviceID string) map[string]interface{} {
	i := SimulateFloat(20, 60)
	v := SimulateFloat(400, 500)
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"kWh": SimulateFloat(100, 500),
			"v":   v,
			"i":   i,
			"kW":  i * v,
		},
	}
}

// 启动一个 MQTT 模拟器，循环发送数据
func startSimulator(client mqtt.Client, deviceID string, interval int, loop bool, payload map[string]interface{}) {
	topic := fmt.Sprintf("sub/devices/telemetry/%s", deviceID)
	for {
		payload := payload
		jsonBytes, _ := json.Marshal(payload)
		token := client.Publish(topic, 0, false, jsonBytes)
		token.Wait()
		fmt.Printf("[Sent] %s -> %s\n", topic, string(jsonBytes))

		if !loop {
			break
		}
		time.Sleep(time.Duration(interval) * time.Second)
	}
}

func main() {
	// MQTT 配置参数
	broker := "tcp://172.24.0.2:1883"
	username := "simulation"
	password := "test1234"
	interval := 1
	loop := true

	flag.Parse()
	rand.Seed(time.Now().UnixNano())

	// 自定义设备 ID 列表
	TIDs := []string{
		"T001", "T002", "T004", "T005", "T015", "T016", "T021", "T022", "T023", "T024",
		"T030", "T031", "T032", "T033", "T034", "T035", "T041", "T048", "T049", "T050",
		"T051", "T060", "T042", "T043", "T044", "T045", "T007", "T008", "T009", "T010",
		"T011", "T017", "T018", "T019", "T020", "T025", "T026", "T027", "T028", "T046",
		"T047", "T048", "T049", "T050", "T051", "T052", "T053", "T054", "T055", "T003",
		"T006", "T014", "T073"}
	MIDs := []string{
		"M001", "M002", "M003", "M004", "M005", "M006", "M007", "M008", "M009", "M010", "M011",
		"M012", "M013", "M014", "M015", "M016", "M052", "M053", "M054", "M055", "M056", "M057",
		"M058", "M059", "M060", "M061", "M062", "M063", "M064", "M065", "M066", "M067", "M068",
		"M069", "M070",
	}

	// 创建 MQTT 客户端
	opts := mqtt.NewClientOptions().
		AddBroker(broker).
		SetClientID(fmt.Sprintf("%s", "simulator-hub")).
		SetUsername(username).
		SetPassword(password)

	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	var wg sync.WaitGroup
	for _, deviceID := range deviceIDs {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()
			startSimulator(client, id, interval, loop)
		}(deviceID)
	}

	wg.Wait()
}
