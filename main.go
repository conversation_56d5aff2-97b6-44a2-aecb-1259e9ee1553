package main

import (
	"bufio"
	"encoding/json"
	"flag"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// 读取txt文件并解析为map[id]name
func loadDeviceMap(filename string) (map[string]string, error) {
	deviceMap := make(map[string]string)

	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("无法打开文件 %s: %v", filename, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNum := 0
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue // 跳过空行
		}

		parts := strings.Split(line, ",")
		if len(parts) != 2 {
			return nil, fmt.Errorf("文件 %s 第 %d 行格式错误: %s", filename, lineNum, line)
		}

		id := strings.TrimSpace(parts[0])
		name := strings.TrimSpace(parts[1])
		deviceMap[id] = name
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取文件 %s 时出错: %v", filename, err)
	}

	return deviceMap, nil
}

// 从map中提取所有的keys
func getKeysFromMap(deviceMap map[string]string) []string {
	keys := make([]string, 0, len(deviceMap))
	for key := range deviceMap {
		keys = append(keys, key)
	}
	return keys
}

// SimulateFloat 生成 [left, right] 范围内的随机 float64 数，保留2位小数
func SimulateFloat(left, right float64) float64 {
	value := float64(left) + rand.Float64()*float64(right-left)
	// 保留2位小数
	return float64(int(value*100+0.5)) / 100
}

// SimulateFloatWithAnchor 生成带锚定值的随机 float64 数，保留2位小数
// anchor: 锚定值，如果为0则不做额外处理，如果>0且在[left,right]范围内，则返回该值的概率更大
// anchorProbability: 锚定值的概率权重 (0.0-1.0)，建议0.3-0.7
func SimulateFloatWithAnchor(left, right float64, anchor float64, anchorProbability float64) float64 {
	// 如果锚定值为0，使用普通的随机数生成
	if anchor == 0 {
		value := float64(left) + rand.Float64()*float64(right-left)
		return float64(int(value*100+0.5)) / 100
	}

	// 检查锚定值是否在有效范围内
	if anchor < float64(left) || anchor > float64(right) {
		value := float64(left) + rand.Float64()*float64(right-left)
		return float64(int(value*100+0.5)) / 100
	}

	// 限制锚定概率在合理范围内
	if anchorProbability < 0 {
		anchorProbability = 0
	}
	if anchorProbability > 1 {
		anchorProbability = 1
	}

	// 根据概率决定是否使用锚定值附近的值
	if rand.Float64() < anchorProbability {
		// 在锚定值附近生成随机数（±10%的范围内）
		rangeSize := float64(right - left)
		deviation := rangeSize * 0.1 // 10%的偏差范围

		minVal := anchor - deviation
		maxVal := anchor + deviation

		// 确保不超出原始范围
		if minVal < float64(left) {
			minVal = float64(left)
		}
		if maxVal > float64(right) {
			maxVal = float64(right)
		}

		value := minVal + rand.Float64()*(maxVal-minVal)
		return float64(int(value*100+0.5)) / 100
	} else {
		// 使用普通的随机数生成
		value := float64(left) + rand.Float64()*float64(right-left)
		return float64(int(value*100+0.5)) / 100
	}
}

// 构建 payload 数据
func buildTransferTankPayload(deviceID string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "transfertank",
		"data": map[string]interface{}{
			"stir_spd":      SimulateFloatWithAnchor(19.8, 20.2, 20.0, 0.8),
			"stir_curr":     SimulateFloatWithAnchor(4.9, 10.0, 5.0, 0.7),
			"press":         SimulateFloatWithAnchor(-1, 5, 5, 0.9),
			"input_weight":  SimulateFloat(900, 1000),
			"output_weight": SimulateFloat(600, 800),
			"rt_weight":     SimulateFloat(500, 600),
		},
	}
}

func buildStirmechPayload(deviceID string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"disperse_spd":  SimulateFloat(100, 500),
			"stir_spd":      SimulateFloat(20, 60),
			"disperse_curr": SimulateFloat(20, 60),
			"stir_curr":     SimulateFloat(20, 60),
			"press":         SimulateFloat(20, 60),
			"temp":          SimulateFloat(20, 60),
			"worktime":      time.Now().Unix(),
		},
	}
}

func buildStirmechECPayload(deviceID string) map[string]interface{} {
	i := SimulateFloatWithAnchor(4.9, 10.0, 5.0, 0.7)
	v := SimulateFloatWithAnchor(400, 450, 425, 0.7)
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"kWh": SimulateFloat(100, 500),
			"v":   v,
			"i":   i,
			"kW":  i * v,
		},
	}
}

func buildStatusPayload(deviceID string, status bool) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"status":    status,
	}
}

func buildEventPayload(deviceID, msgtype string, data []string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      msgtype,
		"data":      data,
	}
}

// 发送单次MQTT消息
func sendMessage(client mqtt.Client, deviceID string, topic string, payloadFunc func(string) map[string]interface{}) {
	payload := payloadFunc(deviceID)
	jsonBytes, _ := json.Marshal(payload)
	token := client.Publish(topic, 0, false, jsonBytes)
	token.Wait()
	fmt.Printf("[Sent] %s -> %s\n", topic, string(jsonBytes))
}

// 启动一个 MQTT 模拟器，循环发送数据
func startSimulator(client mqtt.Client, deviceIDs []string, devType string, interval int, loop bool, payloadFunc func(string) map[string]interface{}, deviceSuffix string) {
	for {
		for _, deviceID := range deviceIDs {
			finalDeviceID := deviceID + deviceSuffix
			topic := fmt.Sprintf("sub/%s/telemetry/%s", devType, finalDeviceID)
			sendMessage(client, finalDeviceID, topic, payloadFunc)
		}

		if !loop {
			break
		}
		time.Sleep(time.Duration(interval) * time.Second)
	}
}

func startEventSimulator(client mqtt.Client, deviceIDs []string, interval int, loop bool, msgtype string, data []string) {
	for {
		for _, deviceID := range deviceIDs {
			topic := fmt.Sprintf("sub/devices/events/%s", deviceID)
			sendMessage(client, deviceID, topic, func(deviceID string) map[string]interface{} {
				return buildEventPayload(deviceID, msgtype, data)
			})
		}
		if !loop {
			break
		}
		time.Sleep(time.Duration(interval) * time.Second)
	}
}

func main() {
	// MQTT 配置参数
	broker := "tcp://192.168.8.245:1883"
	username := "admin"
	password := "test1234"
	interval := 5 // 修改为5秒一次发送
	loop := true

	flag.Parse()
	// 使用新的随机数生成器（Go 1.20+）
	rand.New(rand.NewSource(time.Now().UnixNano()))

	// 读取txt文件并填充对应的map
	var err error

	StirMech, err := loadDeviceMap("stirmech.txt") // 搅拌机
	if err != nil {
		fmt.Printf("加载stirmech.txt失败: %v\n", err)
		return
	}

	TransTank, err := loadDeviceMap("transfer.txt") // 中转罐
	if err != nil {
		fmt.Printf("加载transfer.txt失败: %v\n", err)
		return
	}

	YLJL, err := loadDeviceMap("yljl.txt") // 原料计量
	if err != nil {
		fmt.Printf("加载yljl.txt失败: %v\n", err)
		return
	}

	DIW, err := loadDeviceMap("diw.txt") // DIW
	if err != nil {
		fmt.Printf("加载diw.txt失败: %v\n", err)
		return
	}

	JLTank, err := loadDeviceMap("jiangliao.txt") // 浆料系统
	if err != nil {
		fmt.Printf("加载jiangliao.txt失败: %v\n", err)
		return
	}

	FT, err := loadDeviceMap("FT.txt") // 粉料系统
	if err != nil {
		fmt.Printf("加载FT.txt失败: %v\n", err)
		return
	}

	// 打印加载结果
	fmt.Printf("成功加载设备数据:\n")
	fmt.Printf("- StirMech: %d 个设备\n", len(StirMech))
	fmt.Printf("- TransTank: %d 个设备\n", len(TransTank))
	fmt.Printf("- YLJL: %d 个设备\n", len(YLJL))
	fmt.Printf("- DIW: %d 个设备\n", len(DIW))
	fmt.Printf("- JLTank: %d 个设备\n", len(JLTank))
	fmt.Printf("- FT: %d 个设备\n", len(FT))

	// }
	// events := []string{
	// 	"温度过高", "压力过高", "螺杆泵故障", "排气阀故障", "进料阀故障", "出料阀故障",
	// }

	// 创建 MQTT 客户端
	opts := mqtt.NewClientOptions().
		AddBroker(broker).
		SetClientID("simulator-hub").
		SetUsername(username).
		SetPassword(password)

	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	var wg sync.WaitGroup

	// 从TransTank Map中提取所有设备ID
	transferTankIDs := getKeysFromMap(TransTank)
	// 中转罐部分：创建10个协程同步操作，遍历TransTank Map取出id，执行推送动作
	fmt.Printf("启动中转罐模拟器，共 %d 个设备，使用 10 个协程\n", len(transferTankIDs))
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			// 每个协程处理分配给它的设备
			var assignedDevices []string
			for idx := goroutineID; idx < len(transferTankIDs); idx += 10 {
				assignedDevices = append(assignedDevices, transferTankIDs[idx])
			}
			if len(assignedDevices) > 0 {
				fmt.Printf("协程 %d 负责处理 %d 个中转罐设备: %v\n", goroutineID, len(assignedDevices), assignedDevices)
				startSimulator(client, assignedDevices, "devices", interval, loop, buildTransferTankPayload, "")
			}
		}(i)
	}

	// 从StirMech Map中提取所有设备ID
	stirMechIDs := getKeysFromMap(StirMech)

	// 搅拌机部分：创建10个协程处理常规数据
	fmt.Printf("启动搅拌机常规数据模拟器，共 %d 个设备，使用 10 个协程\n", len(stirMechIDs))

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			// 每个协程处理分配给它的设备
			var assignedDevices []string
			for idx := goroutineID; idx < len(stirMechIDs); idx += 10 {
				assignedDevices = append(assignedDevices, stirMechIDs[idx])
			}

			if len(assignedDevices) > 0 {
				fmt.Printf("协程 %d 负责处理 %d 个搅拌机设备(常规数据): %v\n", goroutineID, len(assignedDevices), assignedDevices)
				startSimulator(client, assignedDevices, "devices", interval, loop, buildStirmechPayload, "")
			}
		}(i)
	}

	// 搅拌机部分：创建10个协程处理电能耗数据
	fmt.Printf("启动搅拌机电能耗数据模拟器，共 %d 个设备，使用 10 个协程\n", len(stirMechIDs))

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			// 每个协程处理分配给它的设备
			var assignedDevices []string
			for idx := goroutineID; idx < len(stirMechIDs); idx += 10 {
				assignedDevices = append(assignedDevices, stirMechIDs[idx])
			}

			if len(assignedDevices) > 0 {
				fmt.Printf("协程 %d 负责处理 %d 个搅拌机设备(电能耗数据): %v\n", goroutineID, len(assignedDevices), assignedDevices)
				startSimulator(client, assignedDevices, "devices", interval, loop, buildStirmechECPayload, "-EC")
			}
		}(i)
	}

	wg.Wait()
}
