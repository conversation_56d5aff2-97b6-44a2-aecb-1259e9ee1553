package main

import (
	"bufio"
	"encoding/json"
	"flag"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// 读取txt文件并解析为map[id]name
func loadDeviceMap(filename string) (map[string]string, error) {
	deviceMap := make(map[string]string)

	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("无法打开文件 %s: %v", filename, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNum := 0
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue // 跳过空行
		}

		parts := strings.Split(line, ",")
		if len(parts) != 2 {
			return nil, fmt.Errorf("文件 %s 第 %d 行格式错误: %s", filename, lineNum, line)
		}

		id := strings.TrimSpace(parts[0])
		name := strings.TrimSpace(parts[1])
		deviceMap[id] = name
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取文件 %s 时出错: %v", filename, err)
	}

	return deviceMap, nil
}

// 从map中提取所有的keys
func getKeysFromMap(deviceMap map[string]string) []string {
	keys := make([]string, 0, len(deviceMap))
	for key := range deviceMap {
		keys = append(keys, key)
	}
	return keys
}

// SimulateFloat 生成 [left, right] 范围内的随机 float64 数
func SimulateFloat(left, right int) float64 {
	return float64(left) + rand.Float64()*float64(right-left)
}

// 构建 payload 数据
func buildTransferTankPayload(deviceID string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "transfertank",
		"data": map[string]interface{}{
			"stir_spd":      SimulateFloat(100, 500),
			"stir_curr":     SimulateFloat(20, 60),
			"press":         SimulateFloat(20, 60),
			"input_weight":  SimulateFloat(20, 60),
			"output_weight": SimulateFloat(20, 60),
			"rt_weight":     SimulateFloat(20, 60),
		},
	}
}

func buildStirmechPayload(deviceID string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"disperse_spd":  SimulateFloat(100, 500),
			"stir_spd":      SimulateFloat(20, 60),
			"disperse_curr": SimulateFloat(20, 60),
			"stir_curr":     SimulateFloat(20, 60),
			"press":         SimulateFloat(20, 60),
			"temp":          SimulateFloat(20, 60),
			"worktime":      time.Now().Unix(),
		},
	}
}

func buildStirmechECPayload(deviceID string) map[string]interface{} {
	i := SimulateFloat(20, 60)
	v := SimulateFloat(400, 500)
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"kWh": SimulateFloat(100, 500),
			"v":   v,
			"i":   i,
			"kW":  i * v,
		},
	}
}

func buildStatusPayload(deviceID string, status bool) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"status":    status,
	}
}

func buildEventPayload(deviceID, msgtype string, data []string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      msgtype,
		"data":      data,
	}
}

// 发送单次MQTT消息
func sendMessage(client mqtt.Client, deviceID string, topic string, payloadFunc func(string) map[string]interface{}) {
	payload := payloadFunc(deviceID)
	jsonBytes, _ := json.Marshal(payload)
	token := client.Publish(topic, 0, false, jsonBytes)
	token.Wait()
	fmt.Printf("[Sent] %s -> %s\n", topic, string(jsonBytes))
}

// 启动一个 MQTT 模拟器，循环发送数据
func startSimulator(client mqtt.Client, deviceIDs []string, interval int, loop bool, payloadFunc func(string) map[string]interface{}, deviceSuffix string) {
	for {
		for _, deviceID := range deviceIDs {
			finalDeviceID := deviceID + deviceSuffix
			topic := fmt.Sprintf("sub/devices/telemetry/%s", finalDeviceID)
			sendMessage(client, finalDeviceID, topic, payloadFunc)
		}

		if !loop {
			break
		}
		time.Sleep(time.Duration(interval) * time.Second)
	}
}

func startEventSimulator(client mqtt.Client, deviceIDs []string, interval int, loop bool, msgtype string, data []string) {
	for {
		for _, deviceID := range deviceIDs {
			topic := fmt.Sprintf("sub/devices/events/%s", deviceID)
			sendMessage(client, deviceID, topic, func(deviceID string) map[string]interface{} {
				return buildEventPayload(deviceID, msgtype, data)
			})
		}
		if !loop {
			break
		}
		time.Sleep(time.Duration(interval) * time.Second)
	}
}

func main() {
	// MQTT 配置参数
	broker := "tcp://172.24.0.2:1883"
	username := "simulation"
	password := "test1234"
	interval := 1
	loop := true

	flag.Parse()
	// 使用新的随机数生成器（Go 1.20+）
	rand.New(rand.NewSource(time.Now().UnixNano()))

	// 读取txt文件并填充对应的map
	var err error

	StirMech, err := loadDeviceMap("stirmech.txt") // 搅拌机
	if err != nil {
		fmt.Printf("加载stirmech.txt失败: %v\n", err)
		return
	}

	TransTank, err := loadDeviceMap("transfer.txt") // 中转罐
	if err != nil {
		fmt.Printf("加载transfer.txt失败: %v\n", err)
		return
	}

	YLJL, err := loadDeviceMap("yljl.txt") // 原料计量
	if err != nil {
		fmt.Printf("加载yljl.txt失败: %v\n", err)
		return
	}

	DIW, err := loadDeviceMap("diw.txt") // DIW
	if err != nil {
		fmt.Printf("加载diw.txt失败: %v\n", err)
		return
	}

	JLTank, err := loadDeviceMap("jiangliao.txt") // 浆料系统
	if err != nil {
		fmt.Printf("加载jiangliao.txt失败: %v\n", err)
		return
	}

	FT, err := loadDeviceMap("FT.txt") // 粉料系统
	if err != nil {
		fmt.Printf("加载FT.txt失败: %v\n", err)
		return
	}

	// 打印加载结果
	fmt.Printf("成功加载设备数据:\n")
	fmt.Printf("- StirMech: %d 个设备\n", len(StirMech))
	fmt.Printf("- TransTank: %d 个设备\n", len(TransTank))
	fmt.Printf("- YLJL: %d 个设备\n", len(YLJL))
	fmt.Printf("- DIW: %d 个设备\n", len(DIW))
	fmt.Printf("- JLTank: %d 个设备\n", len(JLTank))
	fmt.Printf("- FT: %d 个设备\n", len(FT))

	// }
	// events := []string{
	// 	"温度过高", "压力过高", "螺杆泵故障", "排气阀故障", "进料阀故障", "出料阀故障",
	// }

	// 创建 MQTT 客户端
	opts := mqtt.NewClientOptions().
		AddBroker(broker).
		SetClientID("simulator-hub").
		SetUsername(username).
		SetPassword(password)

	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	var wg sync.WaitGroup

	// 第一组：10个协程处理TIDs列表中的所有设备，发送TransferTank消息
	// for i := 0; i < 10; i++ {

	// 	wg.Add(1)
	// 	go func(goroutineID int) {
	// 		defer wg.Done()
	// 		// 每个协程处理分配给它的设备
	// 		var assignedDevices []string
	// 		for idx := goroutineID; idx < len(TIDs); idx += 10 {
	// 			assignedDevices = append(assignedDevices, TIDs[idx])
	// 		}
	// 		startSimulator(client, assignedDevices, interval, loop, buildTransferTankPayload, "")
	// 	}(i)
	// }

	// // 第二组：10个协程处理MIDs列表中的所有设备，发送Stirmech消息
	// for i := 0; i < 10; i++ {
	// 	wg.Add(1)
	// 	go func(goroutineID int) {
	// 		defer wg.Done()
	// 		// 每个协程处理分配给它的设备
	// 		var assignedDevices []string
	// 		for idx := goroutineID; idx < len(MIDs); idx += 10 {
	// 			assignedDevices = append(assignedDevices, MIDs[idx])
	// 		}
	// 		startSimulator(client, assignedDevices, interval, loop, buildStirmechPayload, "")
	// 	}(i)
	// }

	// // 第三组：10个协程处理MIDs列表中的所有设备，发送StirmechEC消息（device_id拼接"-EC"）
	// for i := 0; i < 10; i++ {
	// 	wg.Add(1)
	// 	go func(goroutineID int) {
	// 		defer wg.Done()
	// 		// 每个协程处理分配给它的设备
	// 		var assignedDevices []string
	// 		for idx := goroutineID; idx < len(MIDs); idx += 10 {
	// 			assignedDevices = append(assignedDevices, MIDs[idx])
	// 		}
	// 		startSimulator(client, assignedDevices, interval, loop, buildStirmechECPayload, "-EC")
	// 	}(i)
	// }

	wg.Wait()
}
