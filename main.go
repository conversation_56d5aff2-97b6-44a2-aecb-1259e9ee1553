package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"math/rand"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// SimulateFloat 生成 [left, right] 范围内的随机 float64 数
func SimulateFloat(left, right int) float64 {
	return float64(left) + rand.Float64()*float64(right-left)
}

// 构建 payload 数据
func buildTransferTankPayload(deviceID string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "transfertank",
		"data": map[string]interface{}{
			"stir_spd":      SimulateFloat(100, 500),
			"stir_curr":     SimulateFloat(20, 60),
			"press":         SimulateFloat(20, 60),
			"input_weight":  SimulateFloat(20, 60),
			"output_weight": SimulateFloat(20, 60),
			"rt_weight":     Simulate<PERSON>loat(20, 60),
		},
	}
}

func buildStirmechPayload(deviceID string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"disperse_spd":  SimulateFloat(100, 500),
			"stir_spd":      SimulateFloat(20, 60),
			"disperse_curr": SimulateFloat(20, 60),
			"stir_curr":     SimulateFloat(20, 60),
			"press":         SimulateFloat(20, 60),
			"temp":          SimulateFloat(20, 60),
			"worktime":      time.Now().Unix(),
		},
	}
}

func buildStirmechECPayload(deviceID string) map[string]interface{} {
	i := SimulateFloat(20, 60)
	v := SimulateFloat(400, 500)
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"data": map[string]interface{}{
			"kWh": SimulateFloat(100, 500),
			"v":   v,
			"i":   i,
			"kW":  i * v,
		},
	}
}

func buildStatusPayload(deviceID string, status bool) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      "stirmech",
		"status":    status,
	}
}

func buildEventPayload(deviceID, msgtype string, data []string) map[string]interface{} {
	return map[string]interface{}{
		"ts":        time.Now().UnixMilli(),
		"device_id": deviceID,
		"type":      msgtype,
		"data":      data,
	}
}

// 发送单次MQTT消息
func sendMessage(client mqtt.Client, deviceID string, topic string, payloadFunc func(string) map[string]interface{}) {
	payload := payloadFunc(deviceID)
	jsonBytes, _ := json.Marshal(payload)
	token := client.Publish(topic, 0, false, jsonBytes)
	token.Wait()
	fmt.Printf("[Sent] %s -> %s\n", topic, string(jsonBytes))
}

// 启动一个 MQTT 模拟器，循环发送数据
func startSimulator(client mqtt.Client, deviceIDs []string, interval int, loop bool, payloadFunc func(string) map[string]interface{}, deviceSuffix string) {
	for {
		for _, deviceID := range deviceIDs {
			finalDeviceID := deviceID + deviceSuffix
			topic := fmt.Sprintf("sub/devices/telemetry/%s", finalDeviceID)
			sendMessage(client, finalDeviceID, topic, payloadFunc)
		}

		if !loop {
			break
		}
		time.Sleep(time.Duration(interval) * time.Second)
	}
}

func startEventSimulator(client mqtt.Client, deviceIDs []string, interval int, loop bool, msgtype string, data []string) {
	for {
		for _, deviceID := range deviceIDs {
			topic := fmt.Sprintf("sub/devices/events/%s", deviceID)
			sendMessage(client, deviceID, topic, func(deviceID string) map[string]interface{} {
				return buildEventPayload(deviceID, msgtype, data)
			})
		}
		if !loop {
			break
		}
		time.Sleep(time.Duration(interval) * time.Second)
	}
}

func main() {
	// MQTT 配置参数
	broker := "tcp://172.24.0.2:1883"
	username := "simulation"
	password := "test1234"
	interval := 1
	loop := true

	flag.Parse()
	// 使用新的随机数生成器（Go 1.20+）
	rand.New(rand.NewSource(time.Now().UnixNano()))

	StirMech := map[string]string{} // 搅拌机
	TransTank := map[string]string{} // 中转罐
	YLJL := map[string]string{} // 原料计量
	DIW := map[string]string{} // DIW
	JLTank := map[string]string{} // 浆料系统
	FT := map[string]s
	// 自定义设备 ID 列表
	// TIDs := []string{
	// 	"T001", "T002", "T004", "T005", "T015", "T016", "T021", "T022", "T023", "T024",
	// 	"T030", "T031", "T032", "T033", "T034", "T035", "T041", "T048", "T049", "T050",
	// 	"T051", "T060", "T042", "T043", "T044", "T045", "T007", "T008", "T009", "T010",
	// 	"T011", "T017", "T018", "T019", "T020", "T025", "T026", "T027", "T028", "T046",
	// 	"T047", "T048", "T049", "T050", "T051", "T052", "T053", "T054", "T055", "T003",
	// 	"T006", "T014", "T073"}
	// MIDs := []string{
	// 	"M001", "M002", "M003", "M004", "M005", "M006", "M007", "M008", "M009", "M010", "M011",
	// 	"M012", "M013", "M014", "M015", "M016", "M052", "M053", "M054", "M055", "M056", "M057",
	// 	"M058", "M059", "M060", "M061", "M062", "M063", "M064", "M065", "M066", "M067", "M068",
	// 	"M069", "M070",
	// }

	// events := []string{
	// 	"温度过高", "压力过高", "螺杆泵故障", "排气阀故障", "进料阀故障", "出料阀故障",
	// }

	// 创建 MQTT 客户端
	opts := mqtt.NewClientOptions().
		AddBroker(broker).
		SetClientID("simulator-hub").
		SetUsername(username).
		SetPassword(password)

	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}

	var wg sync.WaitGroup

	// 第一组：10个协程处理TIDs列表中的所有设备，发送TransferTank消息
	for i := 0; i < 10; i++ {

		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			// 每个协程处理分配给它的设备
			var assignedDevices []string
			for idx := goroutineID; idx < len(TIDs); idx += 10 {
				assignedDevices = append(assignedDevices, TIDs[idx])
			}
			startSimulator(client, assignedDevices, interval, loop, buildTransferTankPayload, "")
		}(i)
	}

	// // 第二组：10个协程处理MIDs列表中的所有设备，发送Stirmech消息
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			// 每个协程处理分配给它的设备
			var assignedDevices []string
			for idx := goroutineID; idx < len(MIDs); idx += 10 {
				assignedDevices = append(assignedDevices, MIDs[idx])
			}
			startSimulator(client, assignedDevices, interval, loop, buildStirmechPayload, "")
		}(i)
	}

	// // 第三组：10个协程处理MIDs列表中的所有设备，发送StirmechEC消息（device_id拼接"-EC"）
	// for i := 0; i < 10; i++ {
	// 	wg.Add(1)
	// 	go func(goroutineID int) {
	// 		defer wg.Done()
	// 		// 每个协程处理分配给它的设备
	// 		var assignedDevices []string
	// 		for idx := goroutineID; idx < len(MIDs); idx += 10 {
	// 			assignedDevices = append(assignedDevices, MIDs[idx])
	// 		}
	// 		startSimulator(client, assignedDevices, interval, loop, buildStirmechECPayload, "-EC")
	// 	}(i)
	// }

	// 第四组：10个协程处理TIDs和MIDs列表中所有设备，发送Event消息
	// for i := 0; i < 10; i++ {
	// 	wg.Add(1)
	// 	go func(goroutineID int) {
	// 		defer wg.Done()
	// 		// 每个协程处理分配给它的设备
	// 		var assignedDevices []string
	// 		// for idx := goroutineID; idx < len(TIDs); idx += 10 {
	// 		// 	assignedDevices = append(assignedDevices, TIDs[idx])
	// 		// }
	// 		for idx := goroutineID; idx < len(MIDs); idx += 10 {
	// 			assignedDevices = append(assignedDevices, MIDs[idx])
	// 		}
	// 		startEventSimulator(client, assignedDevices, interval, loop, "stirmech", events[:rand.Intn(len(events))])
	// 	}(i)
	// }

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			// 每个协程处理分配给它的设备
			var assignedDevices []string
			// for idx := goroutineID; idx < len(TIDs); idx += 10 {
			// 	assignedDevices = append(assignedDevices, TIDs[idx])
			// }
			for idx := goroutineID; idx < len(MIDs); idx += 10 {
				assignedDevices = append(assignedDevices, MIDs[idx])
			}

		}(i)
	}

	// fmt.Printf("Started 30 goroutines for MQTT simulation\n")
	// fmt.Printf("- 10 goroutines handling %d TIDs devices (TransferTank)\n", len(TIDs))
	// fmt.Printf("- 10 goroutines handling %d MIDs devices (Stirmech)\n", len(MIDs))
	// fmt.Printf("- 10 goroutines handling %d MIDs devices (StirmechEC)\n", len(MIDs))
	fmt.Printf("- 10 goroutines handling %d TIDs and MIDs devices (Event)\n", len(TIDs)+len(MIDs))
	wg.Wait()
}
